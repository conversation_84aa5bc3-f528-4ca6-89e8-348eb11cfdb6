

"""
完整的BMI预测系统运行脚本
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查依赖"""
    required_packages = [
        'torch',
        'torchvision',
        'facenet_pytorch',
        'opencv-python',
        'scikit-learn',
        'matplotlib',
        'numpy',
        'mtcnn'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            if package == 'opencv-python':
                import cv2
            elif package == 'scikit-learn':
                import sklearn
            elif package == 'facenet_pytorch':
                import facenet_pytorch
            elif package == 'mtcnn':
                import mtcnn  # noqa: F401
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        logger.error("缺少以下依赖包:")
        for package in missing_packages:
            logger.error(f"  pip install {package}")
        return False

    logger.info("所有依赖包已安装")
    return True

def check_input_files():
    """检查输入文件"""
    if not os.path.exists("summary.json"):
        logger.error("找不到 summary.json 文件")
        return False
    
    logger.info("输入文件检查通过")
    return True

def build_face_dataset():
    """构建人脸数据集"""
    logger.info("开始构建人脸数据集")
    
    try:
        from face_detector import FaceDatasetBuilder
        
        builder = FaceDatasetBuilder("face_dataset")
        result = builder.build_dataset("summary.json")
        
        if result and len(result['dataset']) > 0:
            logger.info(f"人脸数据集构建完成，有效样本: {len(result['dataset'])}")
            return True
        else:
            logger.error("人脸数据集构建失败")
            return False
            
    except Exception as e:
        logger.error(f"构建人脸数据集时出错: {e}")
        return False

def train_bmi_model(use_pairing=True):
    """训练BMI预测模型"""
    mode_name = "配对模式" if use_pairing else "单张模式"
    logger.info(f"开始训练BMI预测模型 - {mode_name}")

    try:
        from facenet_bmi_model import PairedBMITrainer

        trainer = PairedBMITrainer("face_dataset/face_dataset.json", "models", use_pairing)
        trainer.load_dataset()
        trainer.create_model()
        trainer.train(epochs=30)
        mae, rmse = trainer.test()

        logger.info(f"模型训练完成 - MAE: {mae:.4f}, RMSE: {rmse:.4f}")
        return True

    except Exception as e:
        logger.error(f"训练模型时出错: {e}")
        return False

def evaluate_model(use_pairing=True):
    """评估模型"""
    mode_name = "配对模式" if use_pairing else "单张模式"
    logger.info(f"评估模型性能 - {mode_name}")

    try:
        from bmi_predictor import BMIEvaluator

        model_path = "models/best_paired_bmi_model.pth" if use_pairing else "models/best_bmi_model.pth"
        dataset_path = "face_dataset/face_dataset.json"

        if not os.path.exists(model_path):
            logger.error(f"模型文件不存在: {model_path}")
            return False

        evaluator = BMIEvaluator(model_path, dataset_path, use_pairing=use_pairing)
        results = evaluator.evaluate_dataset()

        logger.info("模型评估完成")
        logger.info(f"MAE: {results['mae']:.4f}")
        logger.info(f"RMSE: {results['rmse']:.4f}")
        logger.info(f"样本数: {results['num_samples']}")

        return True

    except Exception as e:
        logger.error(f"评估模型时出错: {e}")
        return False

def demo_prediction(use_pairing=True):
    """演示预测功能"""
    mode_name = "配对模式" if use_pairing else "单张模式"
    logger.info(f"演示BMI预测功能 - {mode_name}")

    try:
        from bmi_predictor import PairedBMIInference
        import json

        model_path = "models/best_paired_bmi_model.pth" if use_pairing else "models/best_bmi_model.pth"
        dataset_path = "face_dataset/face_dataset.json"

        if not os.path.exists(model_path):
            logger.error(f"模型文件不存在: {model_path}")
            return False

        # 加载预测器
        predictor = PairedBMIInference(model_path, use_pairing=use_pairing)

        # 读取数据集获取示例图片
        with open(dataset_path, 'r', encoding='utf-8') as f:
            dataset = json.load(f)

        if len(dataset) == 0:
            logger.error("数据集为空")
            return False

        # 选择前几个样本进行演示
        demo_samples = dataset[:5]
        dataset_dir = Path(dataset_path).parent

        logger.info("演示预测结果:")

        for i, sample in enumerate(demo_samples):
            before_path = dataset_dir / sample['before_image_path']
            after_path = dataset_dir / sample['after_image_path']

            if use_pairing and before_path.exists() and after_path.exists():
                # 配对预测
                pred_result = predictor.predict_from_pair(str(before_path), str(after_path))
                if pred_result:
                    actual_start = sample['start_bmi']
                    actual_end = sample['end_bmi']
                    actual_change = actual_end - actual_start

                    logger.info(f"样本 {i+1} (配对预测):")
                    logger.info(f"  起始BMI: 预测={pred_result['start_bmi']:.2f}, 实际={actual_start:.2f}, 误差={abs(pred_result['start_bmi']-actual_start):.2f}")
                    logger.info(f"  结束BMI: 预测={pred_result['end_bmi']:.2f}, 实际={actual_end:.2f}, 误差={abs(pred_result['end_bmi']-actual_end):.2f}")
                    logger.info(f"  BMI变化: 预测={pred_result['bmi_change']:.2f}, 实际={actual_change:.2f}, 误差={abs(pred_result['bmi_change']-actual_change):.2f}")
            else:
                # 单张预测
                if before_path.exists():
                    pred_bmi = predictor.predict_from_face_crop(str(before_path))
                    actual_bmi = sample['start_bmi']

                    if pred_bmi is not None:
                        logger.info(f"样本 {i+1} (Before): 预测BMI={pred_bmi:.2f}, 实际BMI={actual_bmi:.2f}, 误差={abs(pred_bmi-actual_bmi):.2f}")

                if after_path.exists():
                    pred_bmi = predictor.predict_from_face_crop(str(after_path))
                    actual_bmi = sample['end_bmi']

                    if pred_bmi is not None:
                        logger.info(f"样本 {i+1} (After): 预测BMI={pred_bmi:.2f}, 实际BMI={actual_bmi:.2f}, 误差={abs(pred_bmi-actual_bmi):.2f}")

        return True

    except Exception as e:
        logger.error(f"演示预测时出错: {e}")
        return False

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="配对BMI预测系统")
    parser.add_argument("--step", choices=['check', 'dataset', 'train', 'evaluate', 'demo', 'all'],
                       default='all', help="执行步骤")
    parser.add_argument("--pairing", action="store_true", default=True, help="使用配对模式")
    parser.add_argument("--single", action="store_true", help="使用单张模式")

    args = parser.parse_args()

    # 确定使用模式
    use_pairing = not args.single
    mode_name = "配对模式" if use_pairing else "单张模式"

    logger.info(f"BMI预测系统启动 - {mode_name}")

    if args.step in ['check', 'all']:
        logger.info("步骤1: 检查依赖和文件")
        if not check_dependencies():
            return
        if not check_input_files():
            return
        logger.info("检查完成")

    if args.step in ['dataset', 'all']:
        logger.info("步骤2: 构建人脸数据集")
        if not build_face_dataset():
            return
        logger.info("数据集构建完成")

    if args.step in ['train', 'all']:
        logger.info(f"步骤3: 训练BMI预测模型 - {mode_name}")
        if not train_bmi_model(use_pairing):
            return
        logger.info("模型训练完成")

    if args.step in ['evaluate', 'all']:
        logger.info(f"步骤4: 评估模型性能 - {mode_name}")
        if not evaluate_model(use_pairing):
            return
        logger.info("模型评估完成")

    if args.step in ['demo', 'all']:
        logger.info(f"步骤5: 演示预测功能 - {mode_name}")
        if not demo_prediction(use_pairing):
            return
        logger.info("演示完成")

    logger.info("BMI预测系统运行完成")

    # 输出使用说明
    if args.step == 'all':
        model_suffix = "paired_bmi" if use_pairing else "bmi"
        print(f"\n使用说明 ({mode_name}):")

        if use_pairing:
            print("1. 配对图片预测:")
            print(f"   python bmi_predictor.py --model models/best_{model_suffix}_model.pth --pair before.jpg after.jpg")

        print("2. 单张图片预测:")
        print(f"   python bmi_predictor.py --model models/best_{model_suffix}_model.pth --image your_image.jpg")
        print("\n3. 数据集评估:")
        print(f"   python bmi_predictor.py --model models/best_{model_suffix}_model.pth --dataset face_dataset/face_dataset.json --evaluate")
        print("\n4. 重新训练模型:")
        print(f"   python facenet_bmi_model.py --dataset face_dataset/face_dataset.json --epochs 50 {'--pairing' if use_pairing else '--single'}")

if __name__ == "__main__":
    main()
