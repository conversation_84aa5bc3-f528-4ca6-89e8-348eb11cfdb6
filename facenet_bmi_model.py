"""
改进的基于FaceNet的配对BMI预测模型
包含数据增强、注意力机制、多尺度特征融合等优化技术
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from facenet_pytorch import InceptionResnetV1
import cv2
import numpy as np
import json
import os
from pathlib import Path
import logging
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error
import matplotlib.pyplot as plt
import random
from PIL import Image, ImageEnhance, ImageFilter
import torch.nn.functional as F
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
import albumentations as A
from albumentations.pytorch import ToTensorV2

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedDataAugmentation:
    """高级数据增强类"""

    def __init__(self, mode='train'):
        self.mode = mode

        if mode == 'train':
            # 训练时的强数据增强
            self.transform = A.Compose([
                A.HorizontalFlip(p=0.5),
                A.RandomBrightnessContrast(brightness_limit=0.2, contrast_limit=0.2, p=0.7),
                A.HueSaturationValue(hue_shift_limit=10, sat_shift_limit=20, val_shift_limit=10, p=0.5),
                A.GaussNoise(var_limit=(10.0, 50.0), p=0.3),
                A.OneOf([
                    A.MotionBlur(blur_limit=3, p=0.3),
                    A.MedianBlur(blur_limit=3, p=0.3),
                    A.GaussianBlur(blur_limit=3, p=0.3),
                ], p=0.3),
                A.ShiftScaleRotate(shift_limit=0.1, scale_limit=0.1, rotate_limit=15, p=0.5),
                A.CoarseDropout(max_holes=8, max_height=8, max_width=8, p=0.3),
                A.RandomGamma(gamma_limit=(80, 120), p=0.3),
                A.CLAHE(clip_limit=2.0, tile_grid_size=(8, 8), p=0.3),
                A.Resize(160, 160),
                A.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5]),
                ToTensorV2()
            ])
        else:
            # 验证/测试时的轻微增强
            self.transform = A.Compose([
                A.Resize(160, 160),
                A.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5]),
                ToTensorV2()
            ])

    def __call__(self, image):
        if isinstance(image, np.ndarray):
            # 确保图像是RGB格式
            if len(image.shape) == 3 and image.shape[2] == 3:
                return self.transform(image=image)['image']
        return image

class AttentionModule(nn.Module):
    """注意力机制模块"""

    def __init__(self, in_channels, reduction=16):
        super(AttentionModule, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        self.fc = nn.Sequential(
            nn.Linear(in_channels, in_channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels // reduction, in_channels, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()

        # Channel attention
        avg_out = self.fc(self.avg_pool(x).view(b, c))
        max_out = self.fc(self.max_pool(x).view(b, c))

        attention = avg_out + max_out
        attention = attention.view(b, c, 1, 1)

        return x * attention.expand_as(x)

class PairedFaceDataset(Dataset):
    """改进的配对人脸数据集，支持高级数据增强"""

    def __init__(self, data_list, root_dir, augmentation_mode='train', mode='paired', use_mixup=False):
        self.data_list = data_list
        self.root_dir = Path(root_dir)
        self.augmentation = AdvancedDataAugmentation(augmentation_mode)
        self.mode = mode
        self.use_mixup = use_mixup and augmentation_mode == 'train'

        if mode == 'single':
            self.expanded_data = []
            for item in data_list:
                self.expanded_data.append({
                    'image_path': item['before_image_path'],
                    'bmi': item['start_bmi'],
                    'type': 'before'
                })
                self.expanded_data.append({
                    'image_path': item['after_image_path'],
                    'bmi': item['end_bmi'],
                    'type': 'after'
                })
    
    def __len__(self):
        if self.mode == 'paired':
            return len(self.data_list)
        else:
            return len(self.expanded_data)
    
    def _load_image(self, image_path):
        """加载并预处理图像"""
        try:
            image = cv2.imread(str(self.root_dir / image_path))
            if image is None:
                # 如果图像加载失败，创建一个默认图像
                image = np.zeros((160, 160, 3), dtype=np.uint8)
                logger.warning(f"Failed to load image: {image_path}, using default image")
            else:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                # 确保图像尺寸合理
                if image.shape[0] < 32 or image.shape[1] < 32:
                    image = cv2.resize(image, (160, 160))

            # 应用数据增强
            image = self.augmentation(image)

            return image
        except Exception as e:
            logger.error(f"Error loading image {image_path}: {e}")
            # 返回默认图像
            default_image = np.zeros((160, 160, 3), dtype=np.uint8)
            return self.augmentation(default_image)
    
    def _mixup_data(self, x, y, alpha=1.0):
        """Mixup数据增强"""
        if alpha > 0:
            lam = np.random.beta(alpha, alpha)
        else:
            lam = 1

        batch_size = x.size(0)
        index = torch.randperm(batch_size)

        mixed_x = lam * x + (1 - lam) * x[index, :]
        y_a, y_b = y, y[index]
        return mixed_x, y_a, y_b, lam

    def __getitem__(self, idx):
        if self.mode == 'paired':
            item = self.data_list[idx]

            before_image = self._load_image(item['before_image_path'])
            after_image = self._load_image(item['after_image_path'])

            start_bmi = torch.tensor(item['start_bmi'], dtype=torch.float32)
            end_bmi = torch.tensor(item['end_bmi'], dtype=torch.float32)
            bmi_change = end_bmi - start_bmi

            result = {
                'before_image': before_image,
                'after_image': after_image,
                'start_bmi': start_bmi,
                'end_bmi': end_bmi,
                'bmi_change': bmi_change
            }

            # 如果使用mixup，随机选择另一个样本进行混合
            if self.use_mixup and random.random() < 0.5:
                mix_idx = random.randint(0, len(self.data_list) - 1)
                if mix_idx != idx:
                    mix_item = self.data_list[mix_idx]
                    mix_before = self._load_image(mix_item['before_image_path'])
                    mix_after = self._load_image(mix_item['after_image_path'])

                    alpha = 0.2
                    lam = np.random.beta(alpha, alpha)

                    result['before_image'] = lam * before_image + (1 - lam) * mix_before
                    result['after_image'] = lam * after_image + (1 - lam) * mix_after
                    result['mixup_lambda'] = lam
                    result['mix_start_bmi'] = torch.tensor(mix_item['start_bmi'], dtype=torch.float32)
                    result['mix_end_bmi'] = torch.tensor(mix_item['end_bmi'], dtype=torch.float32)

            return result
        else:
            item = self.expanded_data[idx]
            image = self._load_image(item['image_path'])
            bmi = torch.tensor(item['bmi'], dtype=torch.float32)

            return image, bmi

class ImprovedBMIPredictor(nn.Module):
    """改进的BMI预测器，包含注意力机制和多尺度特征融合"""

    def __init__(self, pretrained=True, dropout_rate=0.3, use_pairing=True, use_attention=True):
        super(ImprovedBMIPredictor, self).__init__()

        self.use_pairing = use_pairing
        self.use_attention = use_attention

        # FaceNet特征提取器
        self.facenet = InceptionResnetV1(pretrained='vggface2' if pretrained else None)

        # 渐进式解冻策略 - 解冻更多层以获得更好的特征表示
        layers_to_unfreeze = ['last_linear', 'last_bn', 'avgpool_1a', 'block8', 'conv2d_7b']

        # 首先冻结所有层
        for param in self.facenet.parameters():
            param.requires_grad = False

        # 解冻指定层
        for name, module in self.facenet.named_modules():
            if any(layer_name in name for layer_name in layers_to_unfreeze):
                for param in module.parameters():
                    param.requires_grad = True

        # 添加注意力机制
        if self.use_attention:
            self.attention = AttentionModule(512)

        if use_pairing:
            # 改进的配对模式 - 多尺度特征融合
            self.feature_fusion = nn.Sequential(
                nn.Linear(1024, 768),
                nn.BatchNorm1d(768),
                nn.ReLU(inplace=True),
                nn.Dropout(dropout_rate),
                nn.Linear(768, 512),
                nn.BatchNorm1d(512),
                nn.ReLU(inplace=True),
                nn.Dropout(dropout_rate)
            )

            # 关系建模层 - 更深的网络
            self.relation_layer = nn.Sequential(
                nn.Linear(512, 384),
                nn.BatchNorm1d(384),
                nn.ReLU(inplace=True),
                nn.Dropout(dropout_rate),
                nn.Linear(384, 256),
                nn.BatchNorm1d(256),
                nn.ReLU(inplace=True),
                nn.Dropout(dropout_rate),
                nn.Linear(256, 128),
                nn.BatchNorm1d(128),
                nn.ReLU(inplace=True)
            )

            # 残差连接
            self.residual_connection = nn.Linear(512, 128)

            # 多任务输出头 - 更深的网络
            self.start_bmi_head = nn.Sequential(
                nn.Dropout(dropout_rate),
                nn.Linear(128, 96),
                nn.BatchNorm1d(96),
                nn.ReLU(inplace=True),
                nn.Dropout(dropout_rate),
                nn.Linear(96, 64),
                nn.ReLU(inplace=True),
                nn.Linear(64, 1)
            )

            self.end_bmi_head = nn.Sequential(
                nn.Dropout(dropout_rate),
                nn.Linear(128, 96),
                nn.BatchNorm1d(96),
                nn.ReLU(inplace=True),
                nn.Dropout(dropout_rate),
                nn.Linear(96, 64),
                nn.ReLU(inplace=True),
                nn.Linear(64, 1)
            )

            self.bmi_change_head = nn.Sequential(
                nn.Dropout(dropout_rate),
                nn.Linear(128, 96),
                nn.BatchNorm1d(96),
                nn.ReLU(inplace=True),
                nn.Dropout(dropout_rate),
                nn.Linear(96, 64),
                nn.ReLU(inplace=True),
                nn.Linear(64, 1)
            )
        else:
            # 改进的单张模式
            self.bmi_head = nn.Sequential(
                nn.Dropout(dropout_rate),
                nn.Linear(512, 384),
                nn.BatchNorm1d(384),
                nn.ReLU(inplace=True),
                nn.Dropout(dropout_rate),
                nn.Linear(384, 256),
                nn.BatchNorm1d(256),
                nn.ReLU(inplace=True),
                nn.Dropout(dropout_rate),
                nn.Linear(256, 128),
                nn.BatchNorm1d(128),
                nn.ReLU(inplace=True),
                nn.Dropout(dropout_rate),
                nn.Linear(128, 1)
            )
    
    def forward(self, x):
        if self.use_pairing:
            if isinstance(x, dict):
                before_img = x['before_image']
                after_img = x['after_image']
            else:
                before_img = x[:, 0]
                after_img = x[:, 1]

            # 改进的特征提取
            before_features = self.facenet(before_img)
            after_features = self.facenet(after_img)

            # 应用注意力机制
            if self.use_attention:
                b, c = before_features.shape
                before_features_reshaped = before_features.view(b, c, 1, 1)
                after_features_reshaped = after_features.view(b, c, 1, 1)

                before_features_att = self.attention(before_features_reshaped).view(b, c)
                after_features_att = self.attention(after_features_reshaped).view(b, c)

                before_features = before_features + before_features_att
                after_features = after_features + after_features_att

            # 特征融合
            combined_features = torch.cat([before_features, after_features], dim=1)
            fused_features = self.feature_fusion(combined_features)

            # 关系建模
            relation_features = self.relation_layer(fused_features)

            # 残差连接
            residual = self.residual_connection(fused_features)
            relation_features = relation_features + residual

            # 多任务预测
            start_bmi = self.start_bmi_head(relation_features).squeeze()
            end_bmi = self.end_bmi_head(relation_features).squeeze()
            bmi_change = self.bmi_change_head(relation_features).squeeze()

            return {
                'start_bmi': start_bmi,
                'end_bmi': end_bmi,
                'bmi_change': bmi_change
            }
        else:
            # 单张模式
            features = self.facenet(x)

            # 应用注意力机制
            if self.use_attention:
                b, c = features.shape
                features_reshaped = features.view(b, c, 1, 1)
                features_att = self.attention(features_reshaped).view(b, c)
                features = features + features_att

            bmi = self.bmi_head(features)
            return bmi.squeeze()

# 为了向后兼容，保留原始类名的别名
PairedBMIPredictor = ImprovedBMIPredictor

class ImprovedBMITrainer:
    """改进的BMI训练器，支持高级训练技术"""

    def __init__(self, dataset_path, model_save_dir="models", use_pairing=True,
                 batch_size=16, learning_rate=0.001, use_mixup=False,
                 use_attention=True, use_scheduler=True, early_stopping_patience=15):
        self.dataset_path = dataset_path
        self.model_save_dir = Path(model_save_dir)
        self.model_save_dir.mkdir(exist_ok=True)
        self.use_pairing = use_pairing
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.use_mixup = use_mixup
        self.use_attention = use_attention
        self.use_scheduler = use_scheduler
        self.early_stopping_patience = early_stopping_patience

        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"使用设备: {self.device}")
        logger.info(f"训练模式: {'配对模式' if use_pairing else '单张模式'}")

        self.model = None
        self.optimizer = None
        self.criterion = None
        self.scheduler = None
        self.train_loader = None
        self.val_loader = None
        self.test_loader = None

        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.train_maes = []
        self.val_maes = []

        # 早停机制
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        self.best_model_state = None

    def load_dataset(self):
        """加载数据集并创建数据加载器"""
        logger.info("加载数据集")

        with open(self.dataset_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        logger.info(f"总样本数: {len(data)}")

        # 数据分割
        train_data, temp_data = train_test_split(data, test_size=0.3, random_state=42)
        val_data, test_data = train_test_split(temp_data, test_size=0.5, random_state=42)

        logger.info(f"训练集: {len(train_data)}, 验证集: {len(val_data)}, 测试集: {len(test_data)}")

        root_dir = Path(self.dataset_path).parent

        # 创建数据集
        if self.use_pairing:
            train_dataset = PairedFaceDataset(
                train_data, root_dir,
                augmentation_mode='train',
                mode='paired',
                use_mixup=self.use_mixup
            )
            val_dataset = PairedFaceDataset(
                val_data, root_dir,
                augmentation_mode='val',
                mode='paired',
                use_mixup=False
            )
            test_dataset = PairedFaceDataset(
                test_data, root_dir,
                augmentation_mode='test',
                mode='paired',
                use_mixup=False
            )
        else:
            train_dataset = PairedFaceDataset(
                train_data, root_dir,
                augmentation_mode='train',
                mode='single',
                use_mixup=False
            )
            val_dataset = PairedFaceDataset(
                val_data, root_dir,
                augmentation_mode='val',
                mode='single',
                use_mixup=False
            )
            test_dataset = PairedFaceDataset(
                test_data, root_dir,
                augmentation_mode='test',
                mode='single',
                use_mixup=False
            )

        # 创建数据加载器
        self.train_loader = DataLoader(
            train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=2,
            pin_memory=True
        )
        self.val_loader = DataLoader(
            val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=2,
            pin_memory=True
        )
        self.test_loader = DataLoader(
            test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=2,
            pin_memory=True
        )

        logger.info("数据集加载完成")

    def create_model(self):
        """创建改进的BMI预测模型"""
        logger.info("创建改进的BMI预测模型")

        self.model = ImprovedBMIPredictor(
            pretrained=True,
            use_pairing=self.use_pairing,
            use_attention=self.use_attention,
            dropout_rate=0.3
        )
        self.model.to(self.device)

        # 设置优化器
        if self.use_pairing:
            # 配对模式使用更复杂的损失函数
            self.criterion = self._create_paired_loss()
        else:
            self.criterion = nn.MSELoss()

        # 使用AdamW优化器，具有权重衰减
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.learning_rate,
            weight_decay=0.01,
            betas=(0.9, 0.999)
        )

        # 学习率调度器
        if self.use_scheduler:
            self.scheduler = CosineAnnealingWarmRestarts(
                self.optimizer,
                T_0=10,
                T_mult=2,
                eta_min=1e-6
            )

        logger.info("模型创建完成")

    def _create_paired_loss(self):
        """创建配对模式的复合损失函数"""
        class PairedLoss(nn.Module):
            def __init__(self, alpha=1.0, beta=1.0, gamma=1.0):
                super().__init__()
                self.alpha = alpha  # start_bmi权重
                self.beta = beta    # end_bmi权重
                self.gamma = gamma  # bmi_change权重
                self.mse = nn.MSELoss()
                self.mae = nn.L1Loss()

            def forward(self, predictions, targets, mixup_data=None):
                if mixup_data is not None:
                    # Mixup损失
                    lam = mixup_data['lambda']
                    loss1 = self._compute_loss(predictions, targets)
                    loss2 = self._compute_loss(predictions, mixup_data['targets'])
                    return lam * loss1 + (1 - lam) * loss2
                else:
                    return self._compute_loss(predictions, targets)

            def _compute_loss(self, predictions, targets):
                start_loss = self.mse(predictions['start_bmi'], targets['start_bmi'])
                end_loss = self.mse(predictions['end_bmi'], targets['end_bmi'])
                change_loss = self.mse(predictions['bmi_change'], targets['bmi_change'])

                # 添加L1正则化以提高鲁棒性
                start_l1 = self.mae(predictions['start_bmi'], targets['start_bmi'])
                end_l1 = self.mae(predictions['end_bmi'], targets['end_bmi'])
                change_l1 = self.mae(predictions['bmi_change'], targets['bmi_change'])

                total_loss = (
                    self.alpha * (start_loss + 0.1 * start_l1) +
                    self.beta * (end_loss + 0.1 * end_l1) +
                    self.gamma * (change_loss + 0.1 * change_l1)
                )

                return total_loss

        return PairedLoss()

    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        total_mae = 0
        num_batches = 0

        for batch_idx, batch_data in enumerate(self.train_loader):
            self.optimizer.zero_grad()

            if self.use_pairing:
                # 配对模式
                before_images = batch_data['before_image'].to(self.device)
                after_images = batch_data['after_image'].to(self.device)
                start_bmis = batch_data['start_bmi'].to(self.device)
                end_bmis = batch_data['end_bmi'].to(self.device)
                bmi_changes = batch_data['bmi_change'].to(self.device)

                # 前向传播
                predictions = self.model({
                    'before_image': before_images,
                    'after_image': after_images
                })

                targets = {
                    'start_bmi': start_bmis,
                    'end_bmi': end_bmis,
                    'bmi_change': bmi_changes
                }

                # 处理Mixup
                mixup_data = None
                if 'mixup_lambda' in batch_data:
                    mixup_data = {
                        'lambda': batch_data['mixup_lambda'].to(self.device),
                        'targets': {
                            'start_bmi': batch_data['mix_start_bmi'].to(self.device),
                            'end_bmi': batch_data['mix_end_bmi'].to(self.device),
                            'bmi_change': batch_data['mix_end_bmi'].to(self.device) - batch_data['mix_start_bmi'].to(self.device)
                        }
                    }

                # 计算损失
                loss = self.criterion(predictions, targets, mixup_data)

                # 计算MAE
                mae = (
                    F.l1_loss(predictions['start_bmi'], start_bmis) +
                    F.l1_loss(predictions['end_bmi'], end_bmis) +
                    F.l1_loss(predictions['bmi_change'], bmi_changes)
                ) / 3

            else:
                # 单张模式
                images, bmis = batch_data
                images = images.to(self.device)
                bmis = bmis.to(self.device)

                predictions = self.model(images)
                loss = self.criterion(predictions, bmis)
                mae = F.l1_loss(predictions, bmis)

            # 反向传播
            loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

            self.optimizer.step()

            total_loss += loss.item()
            total_mae += mae.item()
            num_batches += 1

            if batch_idx % 10 == 0:
                logger.info(f'Batch {batch_idx}/{len(self.train_loader)}, Loss: {loss.item():.4f}, MAE: {mae.item():.4f}')

        avg_loss = total_loss / num_batches
        avg_mae = total_mae / num_batches

        return avg_loss, avg_mae

    def validate_epoch(self):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0
        total_mae = 0
        num_batches = 0

        with torch.no_grad():
            for batch_data in self.val_loader:
                if self.use_pairing:
                    # 配对模式
                    before_images = batch_data['before_image'].to(self.device)
                    after_images = batch_data['after_image'].to(self.device)
                    start_bmis = batch_data['start_bmi'].to(self.device)
                    end_bmis = batch_data['end_bmi'].to(self.device)
                    bmi_changes = batch_data['bmi_change'].to(self.device)

                    predictions = self.model({
                        'before_image': before_images,
                        'after_image': after_images
                    })

                    targets = {
                        'start_bmi': start_bmis,
                        'end_bmi': end_bmis,
                        'bmi_change': bmi_changes
                    }

                    loss = self.criterion(predictions, targets)
                    mae = (
                        F.l1_loss(predictions['start_bmi'], start_bmis) +
                        F.l1_loss(predictions['end_bmi'], end_bmis) +
                        F.l1_loss(predictions['bmi_change'], bmi_changes)
                    ) / 3

                else:
                    # 单张模式
                    images, bmis = batch_data
                    images = images.to(self.device)
                    bmis = bmis.to(self.device)

                    predictions = self.model(images)
                    loss = self.criterion(predictions, bmis)
                    mae = F.l1_loss(predictions, bmis)

                total_loss += loss.item()
                total_mae += mae.item()
                num_batches += 1

        avg_loss = total_loss / num_batches
        avg_mae = total_mae / num_batches

        return avg_loss, avg_mae

    def train(self, epochs=50):
        """训练模型"""
        logger.info(f"开始训练，共{epochs}个epoch")

        for epoch in range(epochs):
            logger.info(f"Epoch {epoch+1}/{epochs}")

            # 训练
            train_loss, train_mae = self.train_epoch()

            # 验证
            val_loss, val_mae = self.validate_epoch()

            # 更新学习率
            if self.scheduler:
                self.scheduler.step()

            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.train_maes.append(train_mae)
            self.val_maes.append(val_mae)

            logger.info(f"Train Loss: {train_loss:.4f}, Train MAE: {train_mae:.4f}")
            logger.info(f"Val Loss: {val_loss:.4f}, Val MAE: {val_mae:.4f}")

            # 早停检查
            if val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                self.patience_counter = 0
                self.best_model_state = self.model.state_dict().copy()

                # 保存最佳模型
                torch.save(self.model.state_dict(),
                          self.model_save_dir / 'best_improved_bmi_model.pth')
                logger.info("保存最佳模型")
            else:
                self.patience_counter += 1

            if self.patience_counter >= self.early_stopping_patience:
                logger.info(f"早停触发，在epoch {epoch+1}")
                break

            # 定期保存检查点
            if (epoch + 1) % 10 == 0:
                torch.save(self.model.state_dict(),
                          self.model_save_dir / f'checkpoint_improved_epoch_{epoch+1}.pth')

        # 恢复最佳模型
        if self.best_model_state:
            self.model.load_state_dict(self.best_model_state)

        logger.info("训练完成")

    def evaluate(self):
        """评估模型"""
        logger.info("开始评估模型")

        self.model.eval()
        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for batch_data in self.test_loader:
                if self.use_pairing:
                    before_images = batch_data['before_image'].to(self.device)
                    after_images = batch_data['after_image'].to(self.device)
                    start_bmis = batch_data['start_bmi'].to(self.device)
                    end_bmis = batch_data['end_bmi'].to(self.device)
                    bmi_changes = batch_data['bmi_change'].to(self.device)

                    predictions = self.model({
                        'before_image': before_images,
                        'after_image': after_images
                    })

                    # 收集预测和真实值 - 修复维度问题
                    pred_start = predictions['start_bmi'].cpu().numpy()
                    pred_end = predictions['end_bmi'].cpu().numpy()
                    pred_change = predictions['bmi_change'].cpu().numpy()

                    target_start = start_bmis.cpu().numpy()
                    target_end = end_bmis.cpu().numpy()
                    target_change = bmi_changes.cpu().numpy()

                    # 确保所有数组都是1维的
                    if pred_start.ndim == 0:
                        pred_start = np.array([pred_start])
                    if pred_end.ndim == 0:
                        pred_end = np.array([pred_end])
                    if pred_change.ndim == 0:
                        pred_change = np.array([pred_change])
                    if target_start.ndim == 0:
                        target_start = np.array([target_start])
                    if target_end.ndim == 0:
                        target_end = np.array([target_end])
                    if target_change.ndim == 0:
                        target_change = np.array([target_change])

                    all_predictions.extend([pred_start, pred_end, pred_change])
                    all_targets.extend([target_start, target_end, target_change])
                else:
                    images, bmis = batch_data
                    images = images.to(self.device)
                    bmis = bmis.to(self.device)

                    predictions = self.model(images)

                    pred_array = predictions.cpu().numpy()
                    target_array = bmis.cpu().numpy()

                    if pred_array.ndim == 0:
                        pred_array = np.array([pred_array])
                    if target_array.ndim == 0:
                        target_array = np.array([target_array])

                    all_predictions.append(pred_array)
                    all_targets.append(target_array)

        # 计算指标 - 修复数组连接问题
        if self.use_pairing:
            # 对于配对模式，我们计算所有任务的平均MAE
            all_pred_arrays = []
            all_target_arrays = []

            for pred, target in zip(all_predictions, all_targets):
                all_pred_arrays.extend(pred.flatten())
                all_target_arrays.extend(target.flatten())

            all_predictions_flat = np.array(all_pred_arrays)
            all_targets_flat = np.array(all_target_arrays)
        else:
            all_predictions_flat = np.concatenate(all_predictions)
            all_targets_flat = np.concatenate(all_targets)

        mae = mean_absolute_error(all_targets_flat, all_predictions_flat)
        rmse = np.sqrt(mean_squared_error(all_targets_flat, all_predictions_flat))

        logger.info(f"测试集 MAE: {mae:.4f}")
        logger.info(f"测试集 RMSE: {rmse:.4f}")

        return mae, rmse

class PairedBMITrainer:
    """配对BMI训练器"""
    
    def __init__(self, dataset_path, model_save_dir="models", use_pairing=True):
        self.dataset_path = dataset_path
        self.model_save_dir = Path(model_save_dir)
        self.model_save_dir.mkdir(exist_ok=True)
        self.use_pairing = use_pairing
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"使用设备: {self.device}")
        logger.info(f"训练模式: {'配对模式' if use_pairing else '单张模式'}")
        
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((160, 160)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])
        
        self.model = None
        self.train_loader = None
        self.val_loader = None
        self.test_loader = None
    
    def load_dataset(self):
        logger.info("加载数据集")
        
        with open(self.dataset_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"总样本数: {len(data)}")
        
        train_data, temp_data = train_test_split(data, test_size=0.3, random_state=42)
        val_data, test_data = train_test_split(temp_data, test_size=0.5, random_state=42)
        
        logger.info(f"训练集: {len(train_data)}, 验证集: {len(val_data)}, 测试集: {len(test_data)}")
        
        root_dir = Path(self.dataset_path).parent
        
        if self.use_pairing:
            train_dataset = PairedFaceDataset(train_data, root_dir, self.transform, 'paired')
            val_dataset = PairedFaceDataset(val_data, root_dir, self.transform, 'paired')
            test_dataset = PairedFaceDataset(test_data, root_dir, self.transform, 'paired')
            batch_size = 16
        else:
            train_dataset = PairedFaceDataset(train_data, root_dir, self.transform, 'single')
            val_dataset = PairedFaceDataset(val_data, root_dir, self.transform, 'single')
            test_dataset = PairedFaceDataset(test_data, root_dir, self.transform, 'single')
            batch_size = 32
        
        self.train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)
        self.val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
        self.test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
        
        logger.info("数据集加载完成")
    
    def create_model(self):
        logger.info("创建配对BMI预测模型")
        
        self.model = PairedBMIPredictor(pretrained=True, use_pairing=self.use_pairing)
        self.model.to(self.device)
        
        if self.use_pairing:
            # 配对模式使用多任务损失
            self.criterion = nn.MSELoss()
        else:
            self.criterion = nn.MSELoss()
        
        self.optimizer = optim.Adam(self.model.parameters(), lr=0.001, weight_decay=1e-4)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=5, verbose=True
        )
        
        logger.info("模型创建完成")

    def train_epoch(self):
        self.model.train()
        total_loss = 0
        num_batches = 0

        for batch_idx, batch_data in enumerate(self.train_loader):
            self.optimizer.zero_grad()

            if self.use_pairing:
                # 配对模式
                batch_data = {k: v.to(self.device) for k, v in batch_data.items()}
                predictions = self.model(batch_data)

                # 多任务损失
                start_loss = self.criterion(predictions['start_bmi'], batch_data['start_bmi'])
                end_loss = self.criterion(predictions['end_bmi'], batch_data['end_bmi'])
                change_loss = self.criterion(predictions['bmi_change'], batch_data['bmi_change'])

                # 加权损失
                loss = start_loss + end_loss + 0.5 * change_loss
            else:
                # 单张模式
                images, bmis = batch_data
                images, bmis = images.to(self.device), bmis.to(self.device)
                predictions = self.model(images)
                loss = self.criterion(predictions, bmis)

            loss.backward()
            self.optimizer.step()

            total_loss += loss.item()
            num_batches += 1

            if batch_idx % 50 == 0:
                logger.info(f"Batch {batch_idx}/{len(self.train_loader)}, Loss: {loss.item():.4f}")

        return total_loss / num_batches

    def validate(self):
        self.model.eval()
        total_loss = 0
        predictions_list = []
        targets_list = []

        with torch.no_grad():
            for batch_data in self.val_loader:
                if self.use_pairing:
                    batch_data = {k: v.to(self.device) for k, v in batch_data.items()}
                    predictions = self.model(batch_data)

                    start_loss = self.criterion(predictions['start_bmi'], batch_data['start_bmi'])
                    end_loss = self.criterion(predictions['end_bmi'], batch_data['end_bmi'])
                    change_loss = self.criterion(predictions['bmi_change'], batch_data['bmi_change'])

                    loss = start_loss + end_loss + 0.5 * change_loss

                    # 收集预测结果用于评估
                    predictions_list.extend(predictions['start_bmi'].cpu().numpy())
                    predictions_list.extend(predictions['end_bmi'].cpu().numpy())
                    targets_list.extend(batch_data['start_bmi'].cpu().numpy())
                    targets_list.extend(batch_data['end_bmi'].cpu().numpy())
                else:
                    images, bmis = batch_data
                    images, bmis = images.to(self.device), bmis.to(self.device)
                    predictions = self.model(images)
                    loss = self.criterion(predictions, bmis)

                    predictions_list.extend(predictions.cpu().numpy())
                    targets_list.extend(bmis.cpu().numpy())

                total_loss += loss.item()

        avg_loss = total_loss / len(self.val_loader)
        mae = mean_absolute_error(targets_list, predictions_list)
        rmse = np.sqrt(mean_squared_error(targets_list, predictions_list))

        return avg_loss, mae, rmse

    def train(self, epochs=50):
        logger.info(f"开始训练，共{epochs}个epoch")

        best_val_loss = float('inf')
        train_losses = []
        val_losses = []

        for epoch in range(epochs):
            logger.info(f"Epoch {epoch+1}/{epochs}")

            train_loss = self.train_epoch()
            val_loss, mae, rmse = self.validate()

            self.scheduler.step(val_loss)

            train_losses.append(train_loss)
            val_losses.append(val_loss)

            logger.info(f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, MAE: {mae:.4f}, RMSE: {rmse:.4f}")

            if val_loss < best_val_loss:
                best_val_loss = val_loss
                torch.save(self.model.state_dict(), self.model_save_dir / "best_paired_bmi_model.pth")
                logger.info("保存最佳模型")

        torch.save(self.model.state_dict(), self.model_save_dir / "final_paired_bmi_model.pth")

        # 绘制训练曲线
        plt.figure(figsize=(10, 6))
        plt.plot(train_losses, label='Train Loss')
        plt.plot(val_losses, label='Validation Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Paired BMI Model Training Curves')
        plt.legend()
        plt.grid(True)
        plt.savefig(self.model_save_dir / "paired_training_curves.png")
        plt.close()

        logger.info("训练完成")

    def test(self):
        logger.info("测试模型")

        self.model.load_state_dict(torch.load(self.model_save_dir / "best_paired_bmi_model.pth"))
        self.model.eval()

        predictions_list = []
        targets_list = []

        with torch.no_grad():
            for batch_data in self.test_loader:
                if self.use_pairing:
                    batch_data = {k: v.to(self.device) for k, v in batch_data.items()}
                    predictions = self.model(batch_data)

                    predictions_list.extend(predictions['start_bmi'].cpu().numpy())
                    predictions_list.extend(predictions['end_bmi'].cpu().numpy())
                    targets_list.extend(batch_data['start_bmi'].cpu().numpy())
                    targets_list.extend(batch_data['end_bmi'].cpu().numpy())
                else:
                    images, bmis = batch_data
                    images, bmis = images.to(self.device), bmis.to(self.device)
                    predictions = self.model(images)

                    predictions_list.extend(predictions.cpu().numpy())
                    targets_list.extend(bmis.cpu().numpy())

        mae = mean_absolute_error(targets_list, predictions_list)
        rmse = np.sqrt(mean_squared_error(targets_list, predictions_list))

        logger.info(f"测试结果 - MAE: {mae:.4f}, RMSE: {rmse:.4f}")

        return mae, rmse

def main():
    import argparse

    parser = argparse.ArgumentParser(description="改进的FaceNet BMI预测模型训练")
    parser.add_argument("--dataset", "-d", default="face_dataset/face_dataset.json", help="数据集路径")
    parser.add_argument("--epochs", "-e", type=int, default=50, help="训练轮数")
    parser.add_argument("--model_dir", "-m", default="models", help="模型保存目录")
    parser.add_argument("--pairing", action="store_true", default=True, help="使用配对模式")
    parser.add_argument("--single", action="store_true", help="使用单张模式")
    parser.add_argument("--improved", action="store_true", default=True, help="使用改进的训练器")
    parser.add_argument("--batch_size", "-b", type=int, default=16, help="批次大小")
    parser.add_argument("--learning_rate", "-lr", type=float, default=0.001, help="学习率")
    parser.add_argument("--use_mixup", action="store_true", help="使用Mixup数据增强")
    parser.add_argument("--no_attention", action="store_true", help="不使用注意力机制")
    parser.add_argument("--no_scheduler", action="store_true", help="不使用学习率调度器")

    args = parser.parse_args()

    if not os.path.exists(args.dataset):
        logger.error(f"数据集文件不存在: {args.dataset}")
        return

    use_pairing = not args.single

    if args.improved:
        # 使用改进的训练器
        trainer = ImprovedBMITrainer(
            dataset_path=args.dataset,
            model_save_dir=args.model_dir,
            use_pairing=use_pairing,
            batch_size=args.batch_size,
            learning_rate=args.learning_rate,
            use_mixup=args.use_mixup,
            use_attention=not args.no_attention,
            use_scheduler=not args.no_scheduler,
            early_stopping_patience=15
        )
        trainer.load_dataset()
        trainer.create_model()
        trainer.train(args.epochs)
        mae, rmse = trainer.evaluate()
    else:
        # 使用原始训练器
        trainer = PairedBMITrainer(args.dataset, args.model_dir, use_pairing)
        trainer.load_dataset()
        trainer.create_model()
        trainer.train(args.epochs)
        mae, rmse = trainer.test()

    print(f"\n=== 最终测试结果 ===")
    print(f"MAE: {mae:.4f}")
    print(f"RMSE: {rmse:.4f}")
    print(f"训练器类型: {'改进版' if args.improved else '原始版'}")
    print(f"数据增强: {'启用' if args.improved else '基础'}")
    print(f"注意力机制: {'启用' if args.improved and not args.no_attention else '禁用'}")
    print(f"Mixup: {'启用' if args.use_mixup else '禁用'}")

if __name__ == "__main__":
    main()
