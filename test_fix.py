#!/usr/bin/env python3
"""
测试修复后的模型
"""

import logging
import sys

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_improved_model_without_mixup():
    """测试不使用mixup的改进模型"""
    logger.info("测试改进模型（不使用Mixup）")
    
    try:
        from facenet_bmi_model import ImprovedBMITrainer
        
        # 创建训练器，不使用mixup
        trainer = ImprovedBMITrainer(
            dataset_path="face_dataset/face_dataset.json",
            model_save_dir="models",
            use_pairing=True,
            batch_size=4,
            learning_rate=0.001,
            use_mixup=False,  # 关键：不使用mixup
            use_attention=True,
            use_scheduler=True,
            early_stopping_patience=3
        )
        
        logger.info("加载数据集...")
        trainer.load_dataset()
        
        logger.info("创建模型...")
        trainer.create_model()
        
        logger.info("开始训练1个epoch...")
        trainer.train(epochs=1)
        
        logger.info("评估模型...")
        mae, rmse = trainer.evaluate()
        
        logger.info(f"✅ 测试成功！")
        logger.info(f"MAE: {mae:.4f}")
        logger.info(f"RMSE: {rmse:.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_improved_model_with_mixup():
    """测试使用mixup的改进模型"""
    logger.info("测试改进模型（使用Mixup）")
    
    try:
        from facenet_bmi_model import ImprovedBMITrainer
        
        # 创建训练器，使用mixup
        trainer = ImprovedBMITrainer(
            dataset_path="face_dataset/face_dataset.json",
            model_save_dir="models",
            use_pairing=True,
            batch_size=4,
            learning_rate=0.001,
            use_mixup=True,  # 关键：使用mixup
            use_attention=True,
            use_scheduler=True,
            early_stopping_patience=3
        )
        
        logger.info("加载数据集...")
        trainer.load_dataset()
        
        logger.info("创建模型...")
        trainer.create_model()
        
        logger.info("开始训练1个epoch...")
        trainer.train(epochs=1)
        
        logger.info("评估模型...")
        mae, rmse = trainer.evaluate()
        
        logger.info(f"✅ 测试成功！")
        logger.info(f"MAE: {mae:.4f}")
        logger.info(f"RMSE: {rmse:.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    logger.info("开始测试修复后的模型")
    logger.info("=" * 50)
    
    # 测试1：不使用mixup
    logger.info("🧪 测试1: 不使用Mixup")
    success1 = test_improved_model_without_mixup()
    
    print()
    
    # 测试2：使用mixup
    logger.info("🧪 测试2: 使用Mixup")
    success2 = test_improved_model_with_mixup()
    
    print()
    logger.info("=" * 50)
    
    if success1 and success2:
        logger.info("🎉 所有测试通过！修复成功！")
        logger.info("✅ 不使用Mixup: 正常")
        logger.info("✅ 使用Mixup: 正常")
    elif success1:
        logger.info("⚠️ 部分测试通过")
        logger.info("✅ 不使用Mixup: 正常")
        logger.info("❌ 使用Mixup: 失败")
    elif success2:
        logger.info("⚠️ 部分测试通过")
        logger.info("❌ 不使用Mixup: 失败")
        logger.info("✅ 使用Mixup: 正常")
    else:
        logger.info("❌ 所有测试失败")
        
    return success1 and success2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
