#!/usr/bin/env python3
"""
测试改进的BMI预测模型
"""

import sys
import os
import logging
import traceback

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_improved_model():
    """测试改进的模型"""
    try:
        logger.info("开始测试改进的BMI预测模型")
        
        # 导入模型
        from facenet_bmi_model import ImprovedBMITrainer
        
        # 检查数据集
        dataset_path = "face_dataset/face_dataset.json"
        if not os.path.exists(dataset_path):
            logger.error(f"数据集文件不存在: {dataset_path}")
            return False
        
        # 创建训练器
        trainer = ImprovedBMITrainer(
            dataset_path=dataset_path,
            model_save_dir="models",
            use_pairing=True,
            batch_size=4,  # 使用较小的批次大小
            learning_rate=0.001,
            use_mixup=False,  # 先不使用mixup
            use_attention=True,
            use_scheduler=True,
            early_stopping_patience=5
        )
        
        logger.info("加载数据集...")
        trainer.load_dataset()
        
        logger.info("创建模型...")
        trainer.create_model()
        
        logger.info("开始训练（5个epoch用于测试）...")
        trainer.train(epochs=5)
        
        logger.info("评估模型...")
        mae, rmse = trainer.evaluate()
        
        logger.info(f"测试完成！")
        logger.info(f"MAE: {mae:.4f}")
        logger.info(f"RMSE: {rmse:.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        logger.error(traceback.format_exc())
        return False

def compare_models():
    """比较原始模型和改进模型"""
    try:
        logger.info("开始比较原始模型和改进模型")
        
        from facenet_bmi_model import PairedBMITrainer, ImprovedBMITrainer
        
        dataset_path = "face_dataset/face_dataset.json"
        
        # 测试原始模型
        logger.info("=== 测试原始模型 ===")
        original_trainer = PairedBMITrainer(dataset_path, "models", True)
        original_trainer.load_dataset()
        original_trainer.create_model()
        original_trainer.train(epochs=3)
        original_mae, original_rmse = original_trainer.test()
        
        logger.info(f"原始模型 - MAE: {original_mae:.4f}, RMSE: {original_rmse:.4f}")
        
        # 测试改进模型
        logger.info("=== 测试改进模型 ===")
        improved_trainer = ImprovedBMITrainer(
            dataset_path=dataset_path,
            model_save_dir="models",
            use_pairing=True,
            batch_size=4,
            learning_rate=0.001,
            use_mixup=False,
            use_attention=True,
            use_scheduler=True,
            early_stopping_patience=5
        )
        improved_trainer.load_dataset()
        improved_trainer.create_model()
        improved_trainer.train(epochs=3)
        improved_mae, improved_rmse = improved_trainer.evaluate()
        
        logger.info(f"改进模型 - MAE: {improved_mae:.4f}, RMSE: {improved_rmse:.4f}")
        
        # 比较结果
        mae_improvement = ((original_mae - improved_mae) / original_mae) * 100
        rmse_improvement = ((original_rmse - improved_rmse) / original_rmse) * 100
        
        logger.info("=== 比较结果 ===")
        logger.info(f"MAE 改进: {mae_improvement:.2f}%")
        logger.info(f"RMSE 改进: {rmse_improvement:.2f}%")
        
        if improved_mae < original_mae:
            logger.info("✅ 改进模型的MAE更低！")
        else:
            logger.info("❌ 改进模型的MAE更高")
            
        if improved_rmse < original_rmse:
            logger.info("✅ 改进模型的RMSE更低！")
        else:
            logger.info("❌ 改进模型的RMSE更高")
        
        return True
        
    except Exception as e:
        logger.error(f"比较过程中出现错误: {e}")
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "compare":
        success = compare_models()
    else:
        success = test_improved_model()
    
    if success:
        logger.info("测试成功完成！")
    else:
        logger.error("测试失败！")
        sys.exit(1)
